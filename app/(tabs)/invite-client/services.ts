import { Share, Al<PERSON> } from 'react-native';
import * as Clipboard from 'expo-clipboard';


export const handleShareReferralCode = async (referralCode: string): Promise<void> => {
  try {
    const shareMessage = `Join VetAssist using my referral code: ${referralCode}\n\nDownload the app and connect with your vet for seamless care coordination.`;

    const result = await Share.share({
      message: shareMessage,
      title: 'VetAssist Referral Code',
    });

    if (result.action === Share.sharedAction) {
      console.log('Referral code shared successfully');
    }
  } catch (error) {
    console.error('Error sharing referral code:', error);
    Alert.alert('Error', 'Failed to share referral code');
  }
};

export const handleCopyReferralCode = async (referralCode: string): Promise<void> => {
  try {
    await Clipboard.setStringAsync(referralCode);
    Alert.alert('Copied', 'Referral code copied to clipboard');
  } catch (error) {
    console.error('Error copying referral code:', error);
    Alert.alert('Error', 'Failed to copy referral code');
  }
};

export const isValidReferralCode = (code: string): boolean => {
  return Boolean(code && code.length > 0);
};

export const formatReferralCode = (code: string): string => {
  return code.toUpperCase();
};
